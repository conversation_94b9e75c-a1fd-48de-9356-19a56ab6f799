const fs = require('fs');

// 读取日志文件
const logContent = fs.readFileSync('batch_fix_showAddr.log', 'utf8');

// 提取包含'dryRun mode, skip update'的行
const dryRunLines = logContent.split('\n').filter(line => 
    line.includes('dryRun mode, skip update')
);

console.log(`找到 ${dryRunLines.length} 行包含 'dryRun mode, skip update'`);

// 分析地址格式
const addressAnalysis = [];
const irregularAddresses = [];

dryRunLines.forEach((line, index) => {
    // 提取new:后面的地址
    const newMatch = line.match(/new:([^,]+)/);
    if (newMatch) {
        const newAddress = newMatch[1].trim();
        
        // 检查是否包含城市/省份/邮编信息
        const hasCity = /\b(Toronto|Ottawa|Hamilton|London|Kitchener|Waterloo|Burlington|Cambridge|Mississauga|Brampton|Oakville|Barrie|Guelph|St\. Catharines|Niagara Falls|Windsor|Sudbury|Thunder Bay|Kingston|Peterborough|Oshawa|Markham|Vaughan|Richmond Hill|Newmarket|Aurora|Whitby|Ajax|Pickering|Scarborough|North York|Etobicoke|York|East York|Brantford|Sarnia|Chatham|Leamington|Amherstburg|Tecumseh|LaSalle|Essex|Kingsville|Harrow|Lakeshore|St. Thomas|Woodstock|Ingersoll|Tillsonburg|Simcoe|Delhi|Port Dover|Caledonia|Haldimand|Norfolk|Elgin|Oxford|Perth|Huron|Bruce|Grey|Dufferin|Wellington|Waterloo|Halton|Peel|York|Durham|Kawartha Lakes|Haliburton|Muskoka|Parry Sound|Nipissing|Timiskaming|Cochrane|Algoma|Manitoulin|Sudbury|Greater Sudbury|Thunder Bay|Rainy River|Kenora|ON|Ontario)\b/i.test(newAddress);
        
        const hasPostalCode = /\b[A-Z]\d[A-Z]\s*\d[A-Z]\d\b/i.test(newAddress);
        
        const hasProvince = /\b(ON|Ontario|BC|British Columbia|AB|Alberta|SK|Saskatchewan|MB|Manitoba|QC|Quebec|NB|New Brunswick|NS|Nova Scotia|PE|Prince Edward Island|NL|Newfoundland|NT|Northwest Territories|NU|Nunavut|YT|Yukon)\b/i.test(newAddress);
        
        // 检查是否是非常规格式（包含城市/省份/邮编信息在地址中）
        const isIrregular = hasCity || hasPostalCode || hasProvince;
        
        const analysis = {
            index: index + 1,
            propId: line.match(/propId:([^,]+)/)?.[1] || 'N/A',
            oldAddress: line.match(/old:([^,]+?)(?:,\s*new:)/)?.[1]?.trim() || 'N/A',
            newAddress: newAddress,
            hasCity: hasCity,
            hasPostalCode: hasPostalCode,
            hasProvince: hasProvince,
            isIrregular: isIrregular
        };
        
        addressAnalysis.push(analysis);
        
        if (isIrregular) {
            irregularAddresses.push(analysis);
        }
    }
});

console.log(`\n总共分析了 ${addressAnalysis.length} 个地址`);
console.log(`发现 ${irregularAddresses.length} 个非常规地址格式\n`);

// 输出非常规地址
console.log('=== 非常规地址格式（包含城市/省份/邮编信息）===\n');
irregularAddresses.forEach((addr, index) => {
    console.log(`${index + 1}. PropID: ${addr.propId}`);
    console.log(`   旧地址: ${addr.oldAddress}`);
    console.log(`   新地址: ${addr.newAddress}`);
    console.log(`   包含城市: ${addr.hasCity ? '是' : '否'}`);
    console.log(`   包含邮编: ${addr.hasPostalCode ? '是' : '否'}`);
    console.log(`   包含省份: ${addr.hasProvince ? '是' : '否'}`);
    console.log('   ---');
});

// 导出到CSV文件
const csvContent = [
    'PropID,OldAddress,NewAddress,HasCity,HasPostalCode,HasProvince,IsIrregular'
].concat(
    addressAnalysis.map(addr => 
        `"${addr.propId}","${addr.oldAddress}","${addr.newAddress}",${addr.hasCity},${addr.hasPostalCode},${addr.hasProvince},${addr.isIrregular}`
    )
).join('\n');

fs.writeFileSync('address_analysis.csv', csvContent);
console.log(`\n所有地址分析结果已导出到 address_analysis.csv`);

// 导出非常规地址到单独的CSV
const irregularCsvContent = [
    'PropID,OldAddress,NewAddress,HasCity,HasPostalCode,HasProvince,Issues'
].concat(
    irregularAddresses.map(addr => {
        const issues = [];
        if (addr.hasCity) issues.push('包含城市');
        if (addr.hasPostalCode) issues.push('包含邮编');
        if (addr.hasProvince) issues.push('包含省份');
        return `"${addr.propId}","${addr.oldAddress}","${addr.newAddress}",${addr.hasCity},${addr.hasPostalCode},${addr.hasProvince},"${issues.join(', ')}"`;
    })
).join('\n');

fs.writeFileSync('irregular_addresses.csv', irregularCsvContent);
console.log(`非常规地址已导出到 irregular_addresses.csv`);

// 统计信息
console.log('\n=== 统计信息 ===');
console.log(`总地址数: ${addressAnalysis.length}`);
console.log(`非常规地址数: ${irregularAddresses.length}`);
console.log(`非常规地址比例: ${(irregularAddresses.length / addressAnalysis.length * 100).toFixed(2)}%`);
console.log(`包含城市信息的地址: ${addressAnalysis.filter(a => a.hasCity).length}`);
console.log(`包含邮编信息的地址: ${addressAnalysis.filter(a => a.hasPostalCode).length}`);
console.log(`包含省份信息的地址: ${addressAnalysis.filter(a => a.hasProvince).length}`);
