# 地址格式分析报告

## 概述
本报告分析了 `batch_fix_showAddr.log` 文件中包含 'dryRun mode, skip update' 的行，重点关注其中 'new:' 后面的地址格式，识别出非常规的加拿大地址格式。

## 分析结果统计

### 总体统计
- **总处理行数**: 1,063,338 行
- **成功解析地址数**: 1,063,336 个
- **非常规地址数**: 470,508 个
- **非常规地址比例**: 44.25%

### 问题类型分布
- **包含城市信息的地址**: 53,004 个 (4.98%)
- **包含省份信息的地址**: 437,706 个 (41.17%)
- **包含邮编信息的地址**: 70 个 (0.007%)

## 非常规地址格式类型

### 1. 包含省份信息的地址
这是最常见的问题类型，占非常规地址的绝大多数。

**示例**:
- `318 200 Stinson Street` (原地址包含: Hamilton ON L8N 4J5)
- `114 808 Bronson Avenue` (原地址包含: Dows Lake - Civic Hospital and Area ON K1S 5A4)
- `165 Gerry Lalonde Drive` (原地址包含: Orleans Cumberland and Area ON K4A 5R4)

### 2. 包含城市信息的地址
这些地址在单元号+地址格式中错误地包含了城市名称。

**示例**:
- `133 Eastforest Trail` (包含城市名)
- `801 65 Yorkland Boulevard` (包含城市名 Brampton)
- `1105 15 Wellington Street South Street` (包含城市名 Kitchener)

### 3. 包含邮编信息的地址
这是最少见的问题类型，但最严重，因为邮编不应该出现在地址字段中。

**示例**:
- `Waterloo ON N2V 0H2` (完整的城市+省份+邮编)
- `Cambridge ON N3H 4R8` (完整的城市+省份+邮编)
- `16358 Highway 60 Highway Killaloe Hagarty & Richards ON K0J 2A0`

### 4. 复合问题地址
一些地址同时包含多种问题。

**示例**:
- `1105 15 Wellington Street South Street` (包含城市+省份)
- `227 Wellington Street N` (包含城市+省份)
- `Waterloo ON N2V 0H2` (包含城市+省份+邮编)

## 数据质量问题

### 主要问题
1. **地址标准化不一致**: 原始地址包含完整的城市、省份、邮编信息，但在处理后这些信息被错误地保留在地址字段中
2. **字段分离不当**: 应该分别存储在不同字段的信息（如城市、省份、邮编）被混合在地址字段中
3. **格式不统一**: 同样的地址信息在不同记录中可能有不同的格式

### 影响
- **数据一致性**: 地址格式不统一影响数据的一致性
- **搜索和匹配**: 包含额外信息的地址可能影响地址匹配和搜索功能
- **用户体验**: 显示给用户的地址格式可能不符合预期

## 建议

### 短期修复
1. **清理现有数据**: 对已识别的470,508个非常规地址进行批量清理
2. **标准化处理**: 建立统一的地址格式标准，确保只在地址字段中包含街道地址信息

### 长期改进
1. **数据验证**: 在数据输入和处理过程中添加验证规则
2. **字段分离**: 确保城市、省份、邮编信息存储在专门的字段中
3. **自动化检测**: 建立自动检测机制，及时发现和处理类似问题

## 导出文件

本分析生成了以下文件：
- `address_analysis.csv`: 包含所有地址的完整分析结果
- `irregular_addresses.csv`: 仅包含非常规地址的详细信息

这些文件可用于进一步的数据清理和修复工作。
