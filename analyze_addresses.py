#!/usr/bin/env python3
import re
import csv

# 读取日志文件
with open('batch_fix_showAddr.log', 'r', encoding='utf-8') as f:
    log_content = f.read()

# 提取包含'dryRun mode, skip update'的行
dry_run_lines = [line for line in log_content.split('\n') if 'dryRun mode, skip update' in line]

print(f"找到 {len(dry_run_lines)} 行包含 'dryRun mode, skip update'")

# 加拿大城市列表（主要城市）
canadian_cities = [
    'Toronto', 'Ottawa', 'Hamilton', 'London', 'Kitchener', 'Waterloo', 'Burlington', 'Cambridge',
    'Mississauga', 'Brampton', 'Oakville', 'Barrie', 'Guelph', 'St. Catharines', 'Niagara Falls',
    'Windsor', 'Sudbury', 'Thunder Bay', 'Kingston', 'Peterborough', 'Oshawa', 'Markham', 'Vaughan',
    'Richmond Hill', 'Newmarket', 'Aurora', '<PERSON>hit<PERSON>', 'Ajax', '<PERSON>ering', 'Scarborough', 'North York',
    'Etobicoke', 'York', 'East York', 'Brantford', 'Sarnia', 'Chatham', 'Leamington', 'Amherstburg',
    'Tecum<PERSON><PERSON>', 'LaSalle', 'Essex', 'Kingsville', 'Harrow', 'Lakeshore', 'St. Thomas', 'Woodstock',
    'Ingersoll', 'Tillsonburg', 'Simcoe', 'Delhi', 'Port Dover', 'Caledonia', 'Haldimand', 'Norfolk',
    'Elgin', 'Oxford', 'Perth', 'Huron', 'Bruce', 'Grey', 'Dufferin', 'Wellington', 'Halton', 'Peel',
    'Durham', 'Kawartha Lakes', 'Haliburton', 'Muskoka', 'Parry Sound', 'Nipissing', 'Timiskaming',
    'Cochrane', 'Algoma', 'Manitoulin', 'Greater Sudbury', 'Rainy River', 'Kenora', 'Stittsville',
    'Orleans', 'Nepean', 'Kanata', 'Gloucester', 'Cumberland', 'Rideau', 'Manotick', 'Greely',
    'Metcalfe', 'Osgoode', 'Vernon', 'Carp', 'Dunrobin', 'Huntley', 'Fitzroy', 'Richmond',
    'Munster', 'Constance Bay', 'Kilmaurs', 'Woodlawn', 'Stoney Creek', 'Ancaster', 'Dundas',
    'Flamborough', 'Grimsby', 'Lincoln', 'Pelham', 'Thorold', 'Welland', 'Port Colborne',
    'Fort Erie', 'Niagara-on-the-Lake', 'West Lincoln', 'Wainfleet', 'Elmira', 'New Hamburg',
    'Baden', 'St. Jacobs', 'Conestogo', 'St. Clements', 'Heidelberg', 'Breslau', 'Preston',
    'Hespeler', 'Blair', 'Ayr', 'Paris', 'Burford', 'Mount Pleasant', 'Scotland', 'Oakland',
    'Waterford', 'Simcoe', 'Port Dover', 'Delhi', 'Tillsonburg', 'Aylmer', 'St. Thomas',
    'Dutton', 'Rodney', 'West Lorne', 'Thamesville', 'Bothwell', 'Florence', 'Oil Springs',
    'Petrolia', 'Forest', 'Grand Bend', 'Exeter', 'Hensall', 'Zurich', 'Dashwood', 'Crediton',
    'Centralia', 'Lucan', 'Ailsa Craig', 'Parkhill', 'Strathroy', 'Komoka', 'Delaware',
    'Thorndale', 'Dorchester', 'Ilderton', 'Arva', 'Lambeth', 'Byron', 'Westmount', 'Oakridge',
    'Southdale', 'Pond Mills', 'White Oaks', 'Masonville', 'Stoneybrook', 'Sunningdale',
    'Huron Heights', 'Sherwood Forest', 'Riverbend', 'Riverside', 'East London', 'South London',
    'North London', 'West London', 'Central London', 'Old East Village', 'Old South',
    'Wortley Village', 'SoHo', 'Woodfield', 'Glen Cairn', 'Westmount', 'Oakridge', 'Southcrest'
]

# 加拿大省份缩写和全名
canadian_provinces = [
    'ON', 'Ontario', 'BC', 'British Columbia', 'AB', 'Alberta', 'SK', 'Saskatchewan',
    'MB', 'Manitoba', 'QC', 'Quebec', 'NB', 'New Brunswick', 'NS', 'Nova Scotia',
    'PE', 'Prince Edward Island', 'NL', 'Newfoundland', 'NT', 'Northwest Territories',
    'NU', 'Nunavut', 'YT', 'Yukon'
]

# 分析地址格式
address_analysis = []
irregular_addresses = []

for index, line in enumerate(dry_run_lines):
    # 提取propId
    prop_id_match = re.search(r'propId:([^,]+)', line)
    prop_id = prop_id_match.group(1) if prop_id_match else 'N/A'
    
    # 提取old地址
    old_match = re.search(r'old:([^,]+?)(?:,\s*new:)', line)
    old_address = old_match.group(1).strip() if old_match else 'N/A'
    
    # 提取new地址
    new_match = re.search(r'new:([^,\n]+)', line)
    if new_match:
        new_address = new_match.group(1).strip()
        
        # 检查是否包含城市信息
        has_city = any(city.lower() in new_address.lower() for city in canadian_cities)
        
        # 检查是否包含邮编（加拿大邮编格式：A1A 1A1）
        has_postal_code = bool(re.search(r'\b[A-Z]\d[A-Z]\s*\d[A-Z]\d\b', new_address, re.IGNORECASE))
        
        # 检查是否包含省份信息
        has_province = any(province.lower() in new_address.lower() for province in canadian_provinces)
        
        # 检查是否是非常规格式
        is_irregular = has_city or has_postal_code or has_province
        
        analysis = {
            'index': index + 1,
            'prop_id': prop_id,
            'old_address': old_address,
            'new_address': new_address,
            'has_city': has_city,
            'has_postal_code': has_postal_code,
            'has_province': has_province,
            'is_irregular': is_irregular
        }
        
        address_analysis.append(analysis)
        
        if is_irregular:
            irregular_addresses.append(analysis)

print(f"\n总共分析了 {len(address_analysis)} 个地址")
print(f"发现 {len(irregular_addresses)} 个非常规地址格式\n")

# 输出前20个非常规地址示例
print('=== 非常规地址格式示例（前20个）===\n')
for i, addr in enumerate(irregular_addresses[:20]):
    print(f"{i + 1}. PropID: {addr['prop_id']}")
    print(f"   旧地址: {addr['old_address']}")
    print(f"   新地址: {addr['new_address']}")
    issues = []
    if addr['has_city']:
        issues.append('包含城市')
    if addr['has_postal_code']:
        issues.append('包含邮编')
    if addr['has_province']:
        issues.append('包含省份')
    print(f"   问题: {', '.join(issues)}")
    print('   ---')

# 导出所有地址分析结果到CSV
with open('address_analysis.csv', 'w', newline='', encoding='utf-8') as csvfile:
    fieldnames = ['PropID', 'OldAddress', 'NewAddress', 'HasCity', 'HasPostalCode', 'HasProvince', 'IsIrregular']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    
    writer.writeheader()
    for addr in address_analysis:
        writer.writerow({
            'PropID': addr['prop_id'],
            'OldAddress': addr['old_address'],
            'NewAddress': addr['new_address'],
            'HasCity': addr['has_city'],
            'HasPostalCode': addr['has_postal_code'],
            'HasProvince': addr['has_province'],
            'IsIrregular': addr['is_irregular']
        })

# 导出非常规地址到单独的CSV
with open('irregular_addresses.csv', 'w', newline='', encoding='utf-8') as csvfile:
    fieldnames = ['PropID', 'OldAddress', 'NewAddress', 'HasCity', 'HasPostalCode', 'HasProvince', 'Issues']
    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
    
    writer.writeheader()
    for addr in irregular_addresses:
        issues = []
        if addr['has_city']:
            issues.append('包含城市')
        if addr['has_postal_code']:
            issues.append('包含邮编')
        if addr['has_province']:
            issues.append('包含省份')
        
        writer.writerow({
            'PropID': addr['prop_id'],
            'OldAddress': addr['old_address'],
            'NewAddress': addr['new_address'],
            'HasCity': addr['has_city'],
            'HasPostalCode': addr['has_postal_code'],
            'HasProvince': addr['has_province'],
            'Issues': ', '.join(issues)
        })

print(f"\n所有地址分析结果已导出到 address_analysis.csv")
print(f"非常规地址已导出到 irregular_addresses.csv")

# 统计信息
print('\n=== 统计信息 ===')
print(f"总地址数: {len(address_analysis)}")
print(f"非常规地址数: {len(irregular_addresses)}")
print(f"非常规地址比例: {len(irregular_addresses) / len(address_analysis) * 100:.2f}%")
print(f"包含城市信息的地址: {sum(1 for a in address_analysis if a['has_city'])}")
print(f"包含邮编信息的地址: {sum(1 for a in address_analysis if a['has_postal_code'])}")
print(f"包含省份信息的地址: {sum(1 for a in address_analysis if a['has_province'])}")
